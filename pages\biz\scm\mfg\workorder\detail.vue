<template>
	<view class="work-order-detail">

		<!-- 页面内容 -->
		<view class="page-content" v-if="!loading">
			<!-- Tab导航 -->
			<view class="tab-nav">
				<view
					class="tab-item"
					:class="{ active: activeTab === index }"
					v-for="(tab, index) in tabs"
					:key="index"
					@click="switchTab(index)"
				>
					{{ tab.name }}
				</view>
			</view>

			<!-- Tab内容 -->
			<view class="tab-content">
				<!-- 第一个tab：详情信息 -->
				<view v-if="activeTab === 0" class="tab-pane">
					<!-- 基本信息卡片 -->
					<view class="info-card">
						<view class="card-header">
							<text class="card-title">基本信息</text>
							<view class="status-badge" :style="{ backgroundColor: getStatusColor(workOrderData.status) }">
								{{ getStatusText(workOrderData.status) }}
							</view>
						</view>
						<view class="card-content">
							<view class="info-row">
								<text class="label">生产单号</text>
								<text class="value">{{ workOrderData.workNo || '-' }}</text>
							</view>
							<view class="info-row">
								<text class="label">产品名称</text>
								<text class="value">{{ workOrderData.productName || '-' }}</text>
							</view>
							<view class="info-row">
								<text class="label">产品编号</text>
								<text class="value">{{ workOrderData.productCode || '-' }}</text>
							</view>
							<view class="info-row" v-if="workOrderData.spec">
								<text class="label">规格</text>
								<text class="value">{{ workOrderData.spec }}</text>
							</view>
							<view class="info-row" v-if="workOrderData.customerName">
								<text class="label">客户名称</text>
								<text class="value">{{ workOrderData.customerName }}</text>
							</view>
						</view>
					</view>



					<!-- 进度信息卡片 -->
					<view class="info-card" v-if="workOrderData.progress !== undefined">
						<view class="card-header">
							<text class="card-title">完成进度</text>
							<text class="progress-text">{{ formatProgress(workOrderData.progress) }}%</text>
						</view>
						<view class="card-content">
							<view class="progress-bar">
								<view class="progress-fill" :style="{ width: formatProgress(workOrderData.progress) + '%', backgroundColor: getProgressColor(workOrderData.progress) }"></view>
							</view>
						</view>
					</view>

					<!-- 状态信息卡片 -->
					<view class="info-card" v-if="hasStatusInfo">
						<view class="card-header">
							<text class="card-title">各环节状态</text>
						</view>
						<view class="card-content">
							<view class="status-grid">
								<view class="status-item" v-if="workOrderData.pickingStatus !== undefined">
									<text class="status-label">领料</text>
									<view class="status-badge small" :style="{ backgroundColor: getTaskStatusColor(workOrderData.pickingStatus) }">
										{{ getTaskStatusText(workOrderData.pickingStatus) }}
									</view>
								</view>
								<view class="status-item" v-if="workOrderData.reportStatus !== undefined">
									<text class="status-label">报工</text>
									<view class="status-badge small" :style="{ backgroundColor: getTaskStatusColor(workOrderData.reportStatus) }">
										{{ getTaskStatusText(workOrderData.reportStatus) }}
									</view>
								</view>
								<view class="status-item" v-if="workOrderData.qualityStatus !== undefined">
									<text class="status-label">质检</text>
									<view class="status-badge small" :style="{ backgroundColor: getTaskStatusColor(workOrderData.qualityStatus) }">
										{{ getTaskStatusText(workOrderData.qualityStatus) }}
									</view>
								</view>
								<view class="status-item" v-if="workOrderData.inStockStatus !== undefined">
									<text class="status-label">入库</text>
									<view class="status-badge small" :style="{ backgroundColor: getTaskStatusColor(workOrderData.inStockStatus) }">
										{{ getTaskStatusText(workOrderData.inStockStatus) }}
									</view>
								</view>
							</view>
						</view>
					</view>

					<!-- 数量信息卡片 -->
					<view class="info-card">
						<view class="card-header">
							<text class="card-title">数量信息</text>
						</view>
						<view class="card-content">
							<view class="info-row">
								<text class="label">订单数量</text>
								<text class="value">{{ formatQuantity(workOrderData.orderQuantity) }} {{ getUnitText(workOrderData.orderUnit) }}</text>
							</view>
							<view class="info-row">
								<text class="label">计划数量</text>
								<text class="value">{{ formatQuantity(workOrderData.scheduleQuantity) }} {{ getUnitText(workOrderData.orderUnit) }}</text>
							</view>
							<view class="info-row" v-if="workOrderData.schedulePiece">
								<text class="label">计划件数</text>
								<text class="value">{{ formatQuantity(workOrderData.schedulePiece) }}</text>
							</view>
							<view class="info-row" v-if="workOrderData.actualQuantity">
								<text class="label">实际数量</text>
								<text class="value">{{ formatQuantity(workOrderData.actualQuantity) }} {{ getUnitText(workOrderData.orderUnit) }}</text>
							</view>
							<view class="info-row" v-if="workOrderData.actualPiece">
								<text class="label">实际件数</text>
								<text class="value">{{ formatQuantity(workOrderData.actualPiece) }}</text>
							</view>
						</view>
					</view>

					<!-- 时间信息卡片 -->
					<view class="info-card">
						<view class="card-header">
							<text class="card-title">时间信息</text>
						</view>
						<view class="card-content">
							<view class="info-row" v-if="workOrderData.scheduleStartTime">
								<text class="label">计划开始</text>
								<text class="value">{{ formatDateTime(workOrderData.scheduleStartTime) }}</text>
							</view>
							<view class="info-row" v-if="workOrderData.scheduleEndTime">
								<text class="label">计划结束</text>
								<text class="value">{{ formatDateTime(workOrderData.scheduleEndTime) }}</text>
							</view>
							<view class="info-row" v-if="workOrderData.deliverDate">
								<text class="label">交期</text>
								<text class="value">{{ formatDateTime(workOrderData.deliverDate) }}</text>
							</view>
							<view class="info-row" v-if="workOrderData.actualStartTime">
								<text class="label">实际开始</text>
								<text class="value">{{ formatDateTime(workOrderData.actualStartTime) }}</text>
							</view>
							<view class="info-row" v-if="workOrderData.actualEndTime">
								<text class="label">实际结束</text>
								<text class="value">{{ formatDateTime(workOrderData.actualEndTime) }}</text>
							</view>
						</view>
					</view>

					<!-- BOM和来源信息卡片 -->
					<view class="info-card" v-if="workOrderData.bomCode || workOrderData.orderType || workOrderData.orderNo">
						<view class="card-header">
							<text class="card-title">BOM和来源信息</text>
						</view>
						<view class="card-content">
							<view class="info-row" v-if="workOrderData.bomCode">
								<text class="label">BOM编码</text>
								<text class="value">{{ workOrderData.bomCode }} {{ workOrderData.bomVersion || '' }}</text>
							</view>
							<view class="info-row" v-if="workOrderData.orderType">
								<text class="label">来源类型</text>
								<text class="value">{{ getOrderTypeText(workOrderData.orderType) }}</text>
							</view>
							<view class="info-row" v-if="workOrderData.orderNo">
								<text class="label">来源单号</text>
								<text class="value">{{ workOrderData.orderNo }}</text>
							</view>
						</view>
					</view>

					<!-- 生产信息卡片 -->
					<view class="info-card">
						<view class="card-header">
							<text class="card-title">生产信息</text>
						</view>
						<view class="card-content">
							<view class="info-row" v-if="workOrderData.scheduleLine">
								<text class="label">计划产线</text>
								<text class="value">{{ getScheduleLineText(workOrderData.scheduleLine) }}</text>
							</view>
							<view class="info-row" v-if="workOrderData.actualLine">
								<text class="label">实际产线</text>
								<text class="value">{{ getScheduleLineText(workOrderData.actualLine) }}</text>
							</view>
							<view class="info-row" v-if="workOrderData.scheduleHeadcount">
								<text class="label">计划用人</text>
								<text class="value">{{ workOrderData.scheduleHeadcount }}人</text>
							</view>
							<view class="info-row" v-if="workOrderData.actualHeadcount">
								<text class="label">实际用人</text>
								<text class="value">{{ workOrderData.actualHeadcount }}人</text>
							</view>
							<view class="info-row" v-if="workOrderData.actualBatchNo">
								<text class="label">批号</text>
								<text class="value">{{ workOrderData.actualBatchNo }}</text>
							</view>
						</view>
					</view>

					<!-- 备注信息卡片 -->
					<view class="info-card" v-if="workOrderData.requirement || workOrderData.remark || workOrderData.actualRemark">
						<view class="card-header">
							<text class="card-title">备注信息</text>
						</view>
						<view class="card-content">
							<view class="info-row" v-if="workOrderData.requirement">
								<text class="label">生产要求</text>
								<text class="value">{{ workOrderData.requirement }}</text>
							</view>
							<view class="info-row" v-if="workOrderData.remark">
								<text class="label">备注</text>
								<text class="value">{{ workOrderData.remark }}</text>
							</view>
							<view class="info-row" v-if="workOrderData.actualRemark">
								<text class="label">生产备注</text>
								<text class="value">{{ workOrderData.actualRemark }}</text>
							</view>
						</view>
					</view>

					<!-- 创建信息卡片 -->
					<view class="info-card">
						<view class="card-header">
							<text class="card-title">创建信息</text>
						</view>
						<view class="card-content">
							<view class="info-row" v-if="workOrderData.createTime">
								<text class="label">创建时间</text>
								<text class="value">{{ formatDateTime(workOrderData.createTime) }}</text>
							</view>
							<view class="info-row" v-if="workOrderData.creator">
								<text class="label">创建人</text>
								<text class="value">{{ workOrderData.creator }}</text>
							</view>
						</view>
					</view>
				</view>

				<!-- 第二个tab：投料单信息 -->
				<view v-if="activeTab === 1" class="tab-pane">
					<view v-if="feedOrderList.length > 0">
						<view class="info-card" v-for="(item, index) in feedOrderList" :key="index">
							<view class="card-header">
								<text class="card-title">投料明细 {{ index + 1 }}</text>
								<view class="status-badge" v-if="item.approveStatus"
									:style="{ backgroundColor: getApproveStatusColor(item.approveStatus) }">
									{{ getApproveStatusText(item.approveStatus) }}
								</view>
							</view>
							<view class="card-content">
								<!-- 基本信息 -->
								<view class="info-row">
									<text class="label">序号</text>
									<text class="value">{{ item.num || index + 1 }}</text>
								</view>
								<view class="info-row" v-if="item.warehouseId">
									<text class="label">仓库</text>
									<text class="value">{{ getWarehouseName(item.warehouseId) || item.warehouseId }}</text>
								</view>
								<view class="info-row">
									<text class="label">物料名称</text>
									<text class="value material-name-text">{{ item.materialName || '-' }}</text>
								</view>
								<view class="info-row">
									<text class="label">物料编号</text>
									<text class="value">{{ item.materialCode || '-' }}</text>
								</view>
								<view class="info-row" v-if="item.spec">
									<text class="label">规格</text>
									<text class="value">{{ item.spec }}</text>
								</view>
								<view class="info-row" v-if="item.unit">
									<text class="label">单位</text>
									<text class="value">{{ getUnitName(item.unit) || item.unit }}</text>
								</view>

								<!-- 数量信息 -->
								<view class="info-row">
									<text class="label">计划数量</text>
									<text class="value highlight">{{ formatQuantity(item.plannedQuantity) || '-' }}</text>
								</view>
								<view class="info-row">
									<text class="label">履约数量</text>
									<text class="value highlight">{{ formatQuantity(item.fulfilledQuantity) || '-' }}</text>
								</view>
								<view class="info-row" v-if="item.plannedSpecQuantity">
									<text class="label">规格数量</text>
									<text class="value">{{ formatQuantity(item.plannedSpecQuantity) }}</text>
								</view>
								<view class="info-row" v-if="item.fulfilledSpecQuantity">
									<text class="label">履约规格数量</text>
									<text class="value">{{ formatQuantity(item.fulfilledSpecQuantity) }}</text>
								</view>

								<!-- 其他信息 -->
								<view class="info-row" v-if="item.batchNo">
									<text class="label">批号</text>
									<text class="value">{{ item.batchNo }}</text>
								</view>
								<view class="info-row" v-if="item.note">
									<text class="label">说明</text>
									<text class="value">{{ item.note }}</text>
								</view>
								<view class="info-row" v-if="item.remark">
									<text class="label">备注</text>
									<text class="value">{{ item.remark }}</text>
								</view>
							</view>
						</view>
					</view>
					<view v-else class="empty-state">
						<text class="empty-text">暂无投料单信息</text>
					</view>
				</view>

				<!-- 第三个tab：领料单信息 -->
				<view v-if="activeTab === 2" class="tab-pane">
					<view v-if="pickingReceiptGroups.length > 0">
						<!-- 按领料单分组显示 -->
						<view class="receipt-main-card" v-for="(group, groupIndex) in pickingReceiptGroups" :key="`receipt-${group.groupKey || group.orderNo}-${groupIndex}`">
							<!-- 领料单主信息头部 -->
							<view class="receipt-main-header">
								<view class="main-header-content">
									<text class="main-card-title">
										<text v-if="group.orderNo">{{ group.orderNo }}</text>
										<text v-else>领料单 {{ groupIndex + 1 }}</text>
									</text>
									<text class="main-card-subtitle" v-if="group.bizType">{{ getBizTypeText(group.bizType) }}</text>
								</view>
								<view class="status-badge" v-if="group.approveStatus"
									:style="{ backgroundColor: getApproveStatusColor(group.approveStatus) }">
									{{ getApproveStatusText(group.approveStatus) }}
								</view>
							</view>

							<!-- 主单据基本信息（简化显示） -->
							<view class="receipt-main-info" v-if="group.objectName || group.date || group.approverName">
								<view class="main-info-row" v-if="group.objectName">
									<text class="main-info-label">交易对象：</text>
									<text class="main-info-value">{{ group.objectName }}</text>
								</view>
								<view class="main-info-row" v-if="group.date">
									<text class="main-info-label">日期：</text>
									<text class="main-info-value">{{ formatDate(group.date) }}</text>
								</view>
								<view class="main-info-row" v-if="group.approverName">
									<text class="main-info-label">审批人：</text>
									<text class="main-info-value">{{ group.approverName }}</text>
								</view>
							</view>

							<!-- 物料明细网格（两列布局） -->
							<view class="material-grid">
								<view class="material-item" v-for="(detail, detailIndex) in group.details" :key="detailIndex">
									<!-- 物料卡片头部 -->
									<view class="material-header">
										<text class="material-name">{{ detail.materialName || '未知物料' }}</text>
										<text class="material-code">{{ detail.materialCode || '-' }}</text>
									</view>

									<!-- 物料卡片内容 -->
									<view class="material-content">
										<!-- 规格信息 -->
										<view class="material-spec" v-if="detail.spec">
											<text class="spec-text">{{ detail.spec }}</text>
										</view>

										<!-- 数量信息 -->
										<view class="quantity-section">
											<view class="quantity-item">
												<text class="quantity-label">应领</text>
												<text class="quantity-value planned">
													{{ formatQuantity(detail.plannedQuantity) || '-' }}
													<text v-if="detail.unit" class="unit">{{ getUnitName(detail.unit) || detail.unit }}</text>
												</text>
											</view>
											<view class="quantity-item">
												<text class="quantity-label">实领</text>
												<text class="quantity-value fulfilled">
													{{ formatQuantity(detail.fulfilledQuantity) || '-' }}
													<text v-if="detail.unit" class="unit">{{ getUnitName(detail.unit) || detail.unit }}</text>
												</text>
											</view>
											<!-- 基本单位数量（如果存在） -->
											<view class="quantity-item" v-if="detail.standardPlannedQuantity">
												<text class="quantity-label">基本应领</text>
												<text class="quantity-value standard">
													{{ formatQuantity(detail.standardPlannedQuantity) || '-' }}
													<text v-if="detail.standardUnit" class="unit">{{ getUnitName(detail.standardUnit) || detail.standardUnit }}</text>
												</text>
											</view>
											<view class="quantity-item" v-if="detail.standardFulfilledQuantity">
												<text class="quantity-label">基本实领</text>
												<text class="quantity-value standard">
													{{ formatQuantity(detail.standardFulfilledQuantity) || '-' }}
													<text v-if="detail.standardUnit" class="unit">{{ getUnitName(detail.standardUnit) || detail.standardUnit }}</text>
												</text>
											</view>
										</view>

										<!-- 价格信息（如果存在） -->
										<view class="price-section" v-if="detail.unitPrice || detail.amount">
											<view class="price-item" v-if="detail.unitPrice">
												<text class="price-label">单价</text>
												<text class="price-value">{{ formatAmount(detail.unitPrice) }}</text>
											</view>
											<view class="price-item" v-if="detail.amount">
												<text class="price-label">金额</text>
												<text class="price-value">{{ formatAmount(detail.amount) }}</text>
											</view>
										</view>

										<!-- 其他信息 -->
										<view class="material-extra" v-if="detail.batchNo || detail.warehouseId || detail.remark">
											<view class="extra-item" v-if="detail.batchNo">
												<text class="extra-label">批号：</text>
												<text class="extra-value">{{ detail.batchNo }}</text>
											</view>
											<view class="extra-item" v-if="detail.warehouseId">
												<text class="extra-label">仓库：</text>
												<text class="extra-value">{{ getWarehouseName(detail.warehouseId) }}</text>
											</view>
											<view class="extra-item" v-if="detail.remark">
												<text class="extra-label">备注：</text>
												<text class="extra-value">{{ detail.remark }}</text>
											</view>
										</view>
									</view>
								</view>
							</view>
						</view>
					</view>
					<view v-else class="empty-state">
						<text class="empty-text">暂无领料单信息</text>
					</view>
				</view>

				<!-- 第四个tab：报工单信息 -->
				<view v-if="activeTab === 3" class="tab-pane">
					<view v-if="reportOrderList.length > 0">
						<!-- 报工单详细列表 -->
						<view class="report-list">
							<view class="report-item" v-for="(item, index) in reportOrderList" :key="index">
								<!-- 报工单头部信息 -->
								<view class="report-header">
									<view class="report-title-section">
										<text class="report-code">{{ item.reportCode || '-' }}</text>
										<view class="task-type-badge" v-if="item.type">
											{{ getTaskTypeText(item.type) }}
										</view>
									</view>
									<text class="report-time">{{ formatDateTime(item.createTime) }}</text>
								</view>

								<!-- 时间信息 -->
								<view class="time-section" v-if="item.startTime || item.endTime || item.costTime">
									<view class="section-title">时间信息</view>
									<view class="time-info-grid">
										<view class="time-info-item" v-if="item.startTime">
											<text class="time-label">开始时间</text>
											<text class="time-value">{{ formatDateTime(item.startTime) }}</text>
										</view>
										<view class="time-info-item" v-if="item.endTime">
											<text class="time-label">结束时间</text>
											<text class="time-value">{{ formatDateTime(item.endTime) }}</text>
										</view>
										<view class="time-info-item" v-if="item.costTime">
											<text class="time-label">用时</text>
											<text class="time-value duration">{{ formatDuration(item.costTime) }}</text>
										</view>
									</view>
								</view>

								<!-- 生产数据 -->
								<view class="production-section">
									<view class="section-title">生产数据</view>
									<view class="production-grid">
										<view class="production-item">
											<text class="production-label">数量</text>
											<text class="production-value highlight">{{ formatQuantity(item.quantity) }}</text>
										</view>
										<view class="production-item" v-if="item.piece">
											<text class="production-label">件数</text>
											<text class="production-value highlight">{{ formatQuantity(item.piece) }}</text>
										</view>
										<view class="production-item" v-if="item.costHeadcount">
											<text class="production-label">人数</text>
											<text class="production-value">{{ formatQuantity(item.costHeadcount) }}人</text>
										</view>
										<view class="production-item" v-if="item.line">
											<text class="production-label">生产线</text>
											<text class="production-value">{{ getScheduleLineText(item.line) }}</text>
										</view>
									</view>
								</view>

								<!-- 质量和环境信息 -->
								<view class="quality-section" v-if="item.batchNo || item.temperature || item.humidity">
									<view class="section-title">质量环境</view>
									<view class="quality-grid">
										<view class="quality-item" v-if="item.batchNo">
											<text class="quality-label">批号</text>
											<text class="quality-value">{{ item.batchNo }}</text>
										</view>
										<view class="quality-item" v-if="item.temperature">
											<text class="quality-label">温度</text>
											<text class="quality-value">{{ item.temperature }}°C</text>
										</view>
										<view class="quality-item" v-if="item.humidity">
											<text class="quality-label">湿度</text>
											<text class="quality-value">{{ item.humidity }}%</text>
										</view>
									</view>
								</view>

								<!-- 备注信息 -->
								<view class="remark-section" v-if="item.remark">
									<view class="section-title">备注</view>
									<text class="remark-text">{{ item.remark }}</text>
								</view>
							</view>
						</view>
					</view>
					<view v-else class="empty-state">
						<text class="empty-text">暂无报工单信息</text>
					</view>
				</view>

				<!-- 第五个tab：质检单信息 -->
				<view v-if="activeTab === 4" class="tab-pane">
					<view v-if="inspectionList.length > 0">
						<view class="inspection-list">
							<view class="inspection-item" v-for="(item, index) in inspectionList" :key="index">
								<!-- 质检单头部信息 -->
								<view class="inspection-header">
									<view class="inspection-title-section">
										<text class="inspection-code">{{ item.inspectionCode || '-' }}</text>
										<view class="inspection-result-badge" :class="item.result === 1 ? 'result-pass' : item.result === 2 ? 'result-fail' : item.result === 3 ? 'result-pending' : 'result-unknown'">
											{{ getInspectionResultText(item.result) }}
										</view>
									</view>
									<text class="inspection-time">{{ formatDateTime(item.createTime) }}</text>
								</view>

								<!-- 基本信息 -->
								<view class="inspection-basic-section">
									<view class="section-title">基本信息</view>
									<view class="inspection-basic-grid">
										<view class="inspection-basic-item" v-if="item.sourceType">
											<text class="basic-label">来源类型</text>
											<text class="basic-value">{{ getInspectionSourceTypeText(item.sourceType) }}</text>
										</view>
										<view class="inspection-basic-item" v-if="item.sourceCode">
											<text class="basic-label">来源单号</text>
											<text class="basic-value">{{ item.sourceCode }}</text>
										</view>
										<view class="inspection-basic-item" v-if="item.salesOrderCode">
											<text class="basic-label">销售单号</text>
											<text class="basic-value">{{ item.salesOrderCode }}</text>
										</view>
										<view class="inspection-basic-item" v-if="item.status">
											<text class="basic-label">状态</text>
											<text class="basic-value">{{ getInspectionStatusText(item.status) }}</text>
										</view>
									</view>
								</view>

								<!-- 质检信息 -->
								<view class="inspection-info-section">
									<view class="section-title">质检信息</view>
									<view class="inspection-info-grid">
										<view class="inspection-info-item" v-if="item.inspector">
											<text class="info-label">质检人员</text>
											<text class="info-value">{{ item.inspector }}</text>
										</view>
										<view class="inspection-info-item" v-if="item.inspectionDate">
											<text class="info-label">质检日期</text>
											<text class="info-value">{{ formatDate(item.inspectionDate) }}</text>
										</view>
									</view>
								</view>

								<!-- 备注信息 -->
								<view class="inspection-remark-section" v-if="item.remark">
									<view class="section-title">质检备注</view>
									<text class="inspection-remark-text">{{ item.remark }}</text>
								</view>
							</view>
						</view>
					</view>
					<view v-else class="empty-state">
						<text class="empty-text">暂无质检单信息</text>
					</view>
				</view>

				<!-- 第六个tab：入库单信息 -->
				<view v-if="activeTab === 5" class="tab-pane">
					<view v-if="productReceiptList.length > 0">
						<view class="product-receipt-list">
							<view class="product-receipt-item" v-for="(item, index) in productReceiptList" :key="index">
								<!-- 入库单头部信息 -->
								<view class="product-receipt-header">
									<view class="product-receipt-title-section">
										<text class="product-receipt-code">{{ item.orderNo || '-' }}</text>
										<view class="biz-type-badge">
											{{ getProductBizTypeText(item.bizType) }}
										</view>
									</view>
								</view>

								<!-- 基本信息 -->
								<view class="product-receipt-basic-section">
									<view class="section-title">基本信息</view>
									<view class="product-receipt-basic-grid">
										<view class="product-receipt-basic-item" v-if="item.sourceType">
											<text class="basic-label">来源类型</text>
											<text class="basic-value">{{ getProductSourceTypeText(item.sourceType) }}</text>
										</view>
										<view class="product-receipt-basic-item" v-if="item.sourceNo">
											<text class="basic-label">来源单号</text>
											<text class="basic-value">{{ item.sourceNo }}</text>
										</view>
										<view class="product-receipt-basic-item" v-if="item.objectName">
											<text class="basic-label">交易对象</text>
											<text class="basic-value">{{ item.objectName }}</text>
										</view>
										<view class="product-receipt-basic-item" v-if="item.date">
											<text class="basic-label">交易日期</text>
											<text class="basic-value">{{ formatDate(item.date) }}</text>
										</view>
										<view class="product-receipt-basic-item" v-if="item.approveStatus">
											<text class="basic-label">审批状态</text>
											<text class="basic-value">{{ getApproveStatusText(item.approveStatus) }}</text>
										</view>
										<view class="product-receipt-basic-item" v-if="item.createTime">
											<text class="basic-label">创建时间</text>
											<text class="basic-value">{{ formatDateTime(item.createTime) }}</text>
										</view>
									</view>
								</view>

								<!-- 明细信息 -->
								<view class="product-receipt-details-section" v-if="item.details && item.details.length > 0">
									<view class="section-title">明细信息</view>
									<view class="product-receipt-details">
										<view class="detail-item" v-for="(detail, detailIndex) in item.details" :key="detailIndex">
											<view class="detail-header">
												<text class="detail-material-name">{{ detail.materialName || '未知产品' }}</text>
												<text class="detail-material-code">{{ detail.materialCode || '-' }}</text>
											</view>
											<view class="detail-info-grid">
												<view class="detail-info-item">
													<text class="detail-label">实收数量</text>
													<text class="detail-value highlight">{{ formatQuantity(detail.fulfilledQuantity) }}</text>
												</view>
												<view class="detail-info-item">
													<text class="detail-label">应收数量</text>
													<text class="detail-value">{{ formatQuantity(detail.plannedQuantity) }}</text>
												</view>
												<view class="detail-info-item" v-if="detail.unit">
													<text class="detail-label">单位</text>
													<text class="detail-value">{{ getUnitText(detail.unit) }}</text>
												</view>
												<view class="detail-info-item" v-if="detail.batchNo">
													<text class="detail-label">批号</text>
													<text class="detail-value">{{ detail.batchNo }}</text>
												</view>
												<view class="detail-info-item" v-if="detail.unitPrice">
													<text class="detail-label">单价</text>
													<text class="detail-value">{{ formatAmount(detail.unitPrice) }}</text>
												</view>
												<view class="detail-info-item" v-if="detail.amount">
													<text class="detail-label">金额</text>
													<text class="detail-value">{{ formatAmount(detail.amount) }}</text>
												</view>
											</view>
										</view>
									</view>
								</view>

								<!-- 备注信息 -->
								<view class="product-receipt-remark-section" v-if="item.remark || item.note">
									<view class="section-title">备注信息</view>
									<text class="product-receipt-remark-text" v-if="item.note">摘要：{{ item.note }}</text>
									<text class="product-receipt-remark-text" v-if="item.remark">备注：{{ item.remark }}</text>
								</view>
							</view>
						</view>
					</view>
					<view v-else class="empty-state">
						<text class="empty-text">暂无入库单信息</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 加载状态 -->
		<view v-else class="loading-container">
			<uni-load-more status="loading" :content-text="{ contentdown: '加载中...', contentrefresh: '加载中...', contentnomore: '加载中...' }"></uni-load-more>
		</view>

		<!-- 底部固定操作栏 -->
		<view class="bottom-action-bar" v-if="workOrderData.approveStatus === 3">
			<view class="action-item" @click="handleReportWork">
				<uni-icons type="compose" size="24" color="#007bff"></uni-icons>
				<text class="action-text">报工</text>
			</view>
		</view>
	</view>
</template>

<script>
import { getDictLabel, getBatchDictOptions, DICT_TYPE } from '../../../../../utils/dict';
import { getUnitPageApi } from '../../../../../api/scm/base/unit';
import { getWarehouseListApi } from '../../../../../api/scm/base/warehouse';
import { getWorkOrderApi } from '../../../../../api/scm/mfg/workorder';
import { getWorkOrderDetailListByBizOrderIdApi } from '../../../../../api/scm/mfg/workorderdetail';
import { getPickingReceiptPageApi } from '../../../../../api/scm/inventory/pickingReceipt';
import { getReportOrderPageApi } from '../../../../../api/scm/mfg/reportorder';
import { getInspectionPageApi } from '../../../../../api/scm/quality/inspection';
import { getProductReceiptPageApi } from '../../../../../api/scm/inventory/productreceipt';

export default {
	name: 'WorkOrderDetail',
	data() {
		return {
			// 工单数据
			workOrderData: {},

			// 加载状态
			loading: true,

			// 单位映射
			unitMap: new Map(),

			// Tab相关
			activeTab: 0,
			tabs: [
				{ name: '详情' },
				{ name: '投料' },
				{ name: '领料' },
				{ name: '报工' },
				{ name: '质检' },
				{ name: '入库' }
			],

			// 各个tab的数据
			feedOrderList: [],
			pickingReceiptList: [], // 原始领料单明细数据
			reportOrderList: [],
			inspectionList: [],
			productReceiptList: [],

			// 字典数据
			dictOptions: {
				work_order_status: [],
				approve_status: [],
				common_task_status: [],
				mfg_order_source: [],
				manufacture_line: [],
				inspection_result: [],
				inventory_transaction_type: [],
				mfg_work_type: [],
				inspect_source_type: [],
				inspect_status: [],
				product_source_type: [],
				scm_biz_type: []
			},

			// 单位数据
			unitOptions: [],

			// 仓库数据
			warehouseOptions: []
		}
	},
	
	computed: {
		// 检查是否有状态信息需要显示
		hasStatusInfo() {
			return this.workOrderData.pickingStatus !== undefined ||
				   this.workOrderData.reportStatus !== undefined ||
				   this.workOrderData.qualityStatus !== undefined ||
				   this.workOrderData.inStockStatus !== undefined;
		},

		// 将领料单数据按主单据分组
		pickingReceiptGroups() {
			if (!this.pickingReceiptList || !Array.isArray(this.pickingReceiptList) || this.pickingReceiptList.length === 0) {
				return [];
			}

			// 按领料单分组，使用主单据信息作为分组依据
			const groups = {};
			this.pickingReceiptList.forEach(item => {
				if (!item) return; // 跳过空项

				// 构建分组键：优先使用orderNo，其次使用其他标识字段
				// 这里的关键是要确保同一个领料单的所有明细都有相同的主单据信息
				const groupKey = item.orderNo ||
								`${item.bizType || 'unknown'}-${item.sourceNo || 'unknown'}-${item.createTime || Date.now()}`;

				if (!groups[groupKey]) {
					groups[groupKey] = {
						// 主单据信息
						groupKey: groupKey,
						orderNo: item.orderNo,
						bizType: item.bizType,
						sourceType: item.sourceType,
						sourceNo: item.sourceNo,
						objectName: item.objectName,
						date: item.date,
						approveStatus: item.approveStatus,
						approveNo: item.approveNo,
						approverName: item.approverName,
						approveDate: item.approveDate,
						note: item.note,
						remark: item.remark,
						createTime: item.createTime,
						// 明细数组
						details: []
					};
				}

				// 添加明细到对应的组
				groups[groupKey].details.push({
					materialName: item.materialName,
					materialCode: item.materialCode,
					spec: item.spec,
					unit: item.unit,
					plannedQuantity: item.plannedQuantity,
					fulfilledQuantity: item.fulfilledQuantity,
					standardPlannedQuantity: item.standardPlannedQuantity,
					standardFulfilledQuantity: item.standardFulfilledQuantity,
					standardUnit: item.standardUnit,
					unitPrice: item.unitPrice,
					amount: item.amount,
					taxPrice: item.taxPrice,
					taxAmount: item.taxAmount,
					batchNo: item.batchNo,
					warehouseId: item.warehouseId,
					num: item.num
				});
			});

			// 转换为数组并按创建时间排序
			const result = Object.values(groups).sort((a, b) => {
				const timeA = new Date(a.createTime || 0).getTime();
				const timeB = new Date(b.createTime || 0).getTime();
				return timeB - timeA; // 最新的在前
			});

			return result;
		}
	},
	
	onLoad(options) {
		if (options.id) {
			this.workOrderId = options.id;
			this.initData();
		} else {
			uni.showToast({
				title: '参数错误',
				icon: 'error'
			});
			setTimeout(() => {
				uni.navigateBack();
			}, 1500);
		}
	},
	
	methods: {
		// 处理报工操作
		handleReportWork() {
			// 检查工单状态，只有审核通过的工单才能报工
			if (this.workOrderData.approveStatus !== 3) {
				uni.showToast({
					title: '工单未审核通过，无法报工',
					icon: 'none'
				});
				return;
			}

			// 跳转到报工页面
			uni.navigateTo({
				url: `/pages/biz/scm/mfg/workorder/reportWork?workOrderId=${this.workOrderData.id}&workNo=${this.workOrderData.workNo}`
			});
		},

		// 初始化数据
		async initData() {
			try {
				await Promise.all([
					this.initDictData(),
					this.loadUnitData(),
					this.initWarehouseData(),
					this.loadWorkOrderDetail()
				]);
			} catch (error) {
				console.error('初始化数据失败:', error);
				uni.showToast({
					title: '加载失败',
					icon: 'error'
				});
			} finally {
				this.loading = false;
			}
		},

		// 初始化字典数据
		async initDictData() {
			try {
				const dictTypes = [
					DICT_TYPE.WORK_ORDER_STATUS,
					DICT_TYPE.APPROVE_STATUS,
					DICT_TYPE.COMMON_TASK_STATUS,
					DICT_TYPE.MFG_ORDER_SOURCE,
					DICT_TYPE.MANUFACTURE_LINE,
					DICT_TYPE.INSPECT_RESULT,
					DICT_TYPE.INVENTORY_TRANSACTION_TYPE,
					DICT_TYPE.MFG_WORK_TYPE,
					DICT_TYPE.INSPECT_SOURCE_TYPE,
					DICT_TYPE.INSPECT_STATUS,
					DICT_TYPE.PRODUCT_SOURCE_TYPE,
					DICT_TYPE.SCM_BIZ_TYPE
				];
				const dictMap = await getBatchDictOptions(dictTypes);

				this.dictOptions = {
					work_order_status: dictMap[DICT_TYPE.WORK_ORDER_STATUS] || [],
					approve_status: dictMap[DICT_TYPE.APPROVE_STATUS] || [],
					common_task_status: dictMap[DICT_TYPE.COMMON_TASK_STATUS] || [],
					mfg_order_source: dictMap[DICT_TYPE.MFG_ORDER_SOURCE] || [],
					manufacture_line: dictMap[DICT_TYPE.MANUFACTURE_LINE] || [],
					inspection_result: dictMap[DICT_TYPE.INSPECT_RESULT] || [],
					inventory_transaction_type: dictMap[DICT_TYPE.INVENTORY_TRANSACTION_TYPE] || [],
					mfg_work_type: dictMap[DICT_TYPE.MFG_WORK_TYPE] || [],
					inspect_source_type: dictMap[DICT_TYPE.INSPECT_SOURCE_TYPE] || [],
					inspect_status: dictMap[DICT_TYPE.INSPECT_STATUS] || [],
					product_source_type: dictMap[DICT_TYPE.PRODUCT_SOURCE_TYPE] || [],
					scm_biz_type: dictMap[DICT_TYPE.SCM_BIZ_TYPE] || []
				};
			} catch (error) {
				console.error('获取字典数据失败:', error);
			}
		},

		// 初始化仓库数据
		async initWarehouseData() {
			try {
				const response = await getWarehouseListApi({
					pageNo: 1,
					pageSize: 1000 // 获取所有仓库
				});
				if (response && response.code === 0) {
					// 处理不同的数据结构
					if (response.data) {
						// 如果data是数组，直接使用
						if (Array.isArray(response.data)) {
							this.warehouseOptions = response.data;
						}
						// 如果data是对象且有list属性
						else if (response.data.list && Array.isArray(response.data.list)) {
							this.warehouseOptions = response.data.list;
						}
						// 如果data是对象但没有list属性，可能直接就是仓库数据
						else {
							this.warehouseOptions = [response.data];
						}
					}
					// 兼容旧的数据结构
					else if (response.list && Array.isArray(response.list)) {
						this.warehouseOptions = response.list;
					}
					else {
						this.warehouseOptions = [];
					}
				} else {
					this.warehouseOptions = [];
				}

			} catch (error) {
				console.error('获取仓库数据失败:', error);
				this.warehouseOptions = [];
			}
		},

		// 切换tab
		switchTab(index) {
			this.activeTab = index;
			// 根据tab加载对应数据
			this.loadTabData(index);
		},

		// 加载tab数据
		async loadTabData(tabIndex) {
			if (!this.workOrderData.id) return;

			try {
				switch (tabIndex) {
					case 1: // 投料单
						if (this.feedOrderList.length === 0) {
							await this.loadFeedOrderData();
						}
						break;
					case 2: // 领料单
						if (this.pickingReceiptList.length === 0) {
							await this.loadPickingReceiptData();
						}
						break;
					case 3: // 报工单
						if (this.reportOrderList.length === 0) {
							await this.loadReportOrderData();
						}
						break;
					case 4: // 质检单
						if (this.inspectionList.length === 0) {
							await this.loadInspectionData();
						}
						break;
					case 5: // 入库单
						if (this.productReceiptList.length === 0) {
							await this.loadProductReceiptData();
						}
						break;
				}
			} catch (error) {
				console.error('加载tab数据失败:', error);
			}
		},

		// 加载投料单数据
		async loadFeedOrderData() {
			try {
				const response = await getWorkOrderDetailListByBizOrderIdApi(this.workOrderData.id);
				if (response.code === 0 && response.data) {
					this.feedOrderList = response.data;
				}
			} catch (error) {
				console.error('加载投料单数据失败:', error);
				// 如果API不存在，设置空数组
				this.feedOrderList = [];
			}
		},

		// 加载领料单数据
		async loadPickingReceiptData() {
			try {
				// 参考PC端实现：先使用getPickingReceiptPage获取领料单列表
				// 使用工单ID作为sourceId查询领料单
				const pickingReceiptPageResponse = await getPickingReceiptPageApi({
					sourceId: this.workOrderData.id, // 使用工单ID作为来源单据ID
					pageNo: 1,
					pageSize: 100
				});

				if (pickingReceiptPageResponse && pickingReceiptPageResponse.code === 0) {
					const pickingReceiptList = pickingReceiptPageResponse.data?.list || [];

					if (pickingReceiptList.length > 0) {
						// 直接使用领料单中的details字段，扁平化处理
						const allDetails = [];
						pickingReceiptList.forEach(receipt => {
							const details = receipt.details || [];
							if (details.length > 0) {
								// 将主单据信息合并到每个明细中
								details.forEach(detail => {
									allDetails.push({
										...detail,
										// 添加主单据信息
										orderNo: receipt.orderNo,
										bizType: receipt.bizType,
										sourceType: receipt.sourceType,
										sourceNo: receipt.sourceNo,
										date: receipt.date,
										approveStatus: receipt.approveStatus,
										approveNo: receipt.approveNo,
										approverName: receipt.approverName,
										approveDate: receipt.approveDate,
										note: receipt.note || detail.note,
										remark: receipt.remark || detail.remark,
										createTime: receipt.createTime
									});
								});
							} else {
								// 如果没有明细，至少显示主单据信息
								allDetails.push({
									...receipt,
									materialName: receipt.materialName || '未知物料',
									materialCode: receipt.materialCode || '-'
								});
							}
						});

						this.pickingReceiptList = allDetails;
					} else {
						this.pickingReceiptList = [];
					}
				} else {
					this.pickingReceiptList = [];
				}
			} catch (error) {
				console.error('加载领料单数据失败:', error);
				// 如果API调用失败，尝试使用工单明细数据作为备选
				try {
					const fallbackData = await getWorkOrderDetailListByBizOrderIdApi(this.workOrderData.id);
					if (fallbackData && fallbackData.code === 0 && fallbackData.data) {
						this.pickingReceiptList = Array.isArray(fallbackData.data) ? fallbackData.data : [];
					} else {
						this.pickingReceiptList = [];
					}
				} catch (fallbackError) {
					console.error('获取工单明细数据也失败:', fallbackError);
					this.pickingReceiptList = [];
				}
			}
		},

		// 加载报工单数据
		async loadReportOrderData() {
			try {
				// 使用分页接口查询，根据工单ID过滤
				const response = await getReportOrderPageApi({
					pageNo: 1,
					pageSize: 100,
					workId: this.workOrderData.id
				});
				if (response.code === 0 && response.data && response.data.list) {
					this.reportOrderList = response.data.list;
				}
			} catch (error) {
				console.error('加载报工单数据失败:', error);
				// 如果API不存在，设置空数组
				this.reportOrderList = [];
			}
		},

		// 加载质检单数据
		async loadInspectionData() {
			try {
				if (!this.workOrderData || !this.workOrderData.workNo) {
					this.inspectionList = [];
					return;
				}

				const response = await getInspectionPageApi({
					pageNo: 1,
					pageSize: 100,
					sourceCode: this.workOrderData.workNo
				});

				if (response && response.code === 0) {
					if (response.data && Array.isArray(response.data.list)) {
						this.inspectionList = response.data.list;
					} else if (Array.isArray(response.data)) {
						this.inspectionList = response.data;
					} else {
						this.inspectionList = [];
					}
				} else {
					this.inspectionList = [];
				}
			} catch (error) {
				console.error('加载质检单数据失败:', error);
				this.inspectionList = [];
			}
		},

		// 加载入库单数据
		async loadProductReceiptData() {
			try {
				if (!this.workOrderData || !this.workOrderData.workNo) {
					this.productReceiptList = [];
					return;
				}

				const response = await getProductReceiptPageApi({
					pageNo: 1,
					pageSize: 100,
					sourceNo: this.workOrderData.workNo
				});

				if (response && response.code === 0) {
					if (response.data && Array.isArray(response.data.list)) {
						this.productReceiptList = response.data.list;
						// 确保每个入库单都有明细数据
						this.productReceiptList.forEach(order => {
							if (!order.details && order.productReceiptDetails) {
								order.details = order.productReceiptDetails;
							}
						});
					} else if (Array.isArray(response.data)) {
						this.productReceiptList = response.data;
					} else {
						this.productReceiptList = [];
					}
				} else {
					this.productReceiptList = [];
				}
			} catch (error) {
				console.error('加载产品入库单数据失败:', error);
				this.productReceiptList = [];
			}
		},

		// 加载单位数据
		async loadUnitData() {
			try {
				const response = await getUnitPageApi({ pageNo: 1, pageSize: 100 });
				if (response.code === 0 && response.data && response.data.list) {
					response.data.list.forEach(unit => {
						this.unitMap.set(unit.id, unit.name);
					});
				}
			} catch (error) {
				console.error('加载单位数据失败:', error);
			}
		},

		// 加载工单详情
		async loadWorkOrderDetail() {
			try {
				const response = await getWorkOrderApi(this.workOrderId);
				if (response.code === 0 && response.data) {
					this.workOrderData = response.data;
				} else {
					throw new Error('获取工单详情失败');
				}
			} catch (error) {
				console.error('加载工单详情失败:', error);
				uni.showToast({
					title: '加载工单详情失败',
					icon: 'error'
				});
			}
		},

		// 返回上一页
		goBack() {
			uni.navigateBack();
		},

		// 获取状态文本
		getStatusText(status) {
			const options = this.dictOptions?.work_order_status;
			if (!options || !Array.isArray(options) || options.length === 0) {
				return status || '未知状态';
			}
			return getDictLabel(options, status) || '未知状态';
		},

		// 获取任务状态文本
		getTaskStatusText(status) {
			const options = this.dictOptions?.common_task_status;
			if (!options || !Array.isArray(options) || options.length === 0) {
				return status || '未知';
			}
			return getDictLabel(options, status) || '未知';
		},

		// 获取订单来源类型文本
		getOrderTypeText(orderType) {
			const options = this.dictOptions?.mfg_order_source;
			if (!options || !Array.isArray(options) || options.length === 0) {
				return orderType || '未知';
			}
			return getDictLabel(options, orderType) || orderType;
		},

		// 获取生产线文本
		getScheduleLineText(scheduleLine) {
			const options = this.dictOptions?.manufacture_line;
			if (!options || !Array.isArray(options) || options.length === 0) {
				return scheduleLine || '未知';
			}
			return getDictLabel(options, scheduleLine) || scheduleLine;
		},

		// 获取单位文本
		getUnitText(unit) {
			if (!unit) return '';
			const unitId = typeof unit === 'string' ? parseInt(unit) : unit;
			const unitName = this.unitMap.get(unitId);
			return unitName || unit.toString();
		},

		// 格式化数量
		formatQuantity(quantity) {
			if (quantity === null || quantity === undefined) return '0';
			return parseFloat(quantity).toLocaleString();
		},

		// 格式化日期时间
		formatDateTime(timestamp) {
			if (!timestamp) return '-';
			const date = new Date(timestamp);
			const year = date.getFullYear();
			const month = String(date.getMonth() + 1).padStart(2, '0');
			const day = String(date.getDate()).padStart(2, '0');
			const hours = String(date.getHours()).padStart(2, '0');
			const minutes = String(date.getMinutes()).padStart(2, '0');
			return `${year}-${month}-${day} ${hours}:${minutes}`;
		},

		// 格式化日期（不包含时间）
		formatDate(timestamp) {
			if (!timestamp) return '-';
			const date = new Date(timestamp);
			const year = date.getFullYear();
			const month = String(date.getMonth() + 1).padStart(2, '0');
			const day = String(date.getDate()).padStart(2, '0');
			return `${year}-${month}-${day}`;
		},

		// 格式化进度
		formatProgress(progress) {
			if (progress === null || progress === undefined) return 0;
			const num = parseFloat(progress);
			return isNaN(num) ? 0 : Math.min(100, Math.max(0, num));
		},

		// 获取状态颜色
		getStatusColor(status) {
			const statusNum = parseInt(status) || 0;
			if (statusNum === 0) return '#495057'; // 深灰色 - 未开始
			if (statusNum === 1) return '#fd7e14'; // 橙色 - 进行中
			if (statusNum === 2) return '#0d6efd'; // 深蓝色 - 处理中
			if (statusNum >= 3) return '#198754';  // 深绿色 - 完成
			return '#495057';
		},

		// 获取任务状态颜色
		getTaskStatusColor(status) {
			const statusNum = parseInt(status) || 0;
			if (statusNum === 0) return '#495057'; // 深灰色 - 未开始
			if (statusNum === 1) return '#fd7e14'; // 橙色 - 进行中
			if (statusNum === 2) return '#0d6efd'; // 深蓝色 - 处理中
			if (statusNum >= 3) return '#198754';  // 深绿色 - 完成
			return '#495057';
		},

		// 获取进度条颜色
		getProgressColor(progress) {
			const num = this.formatProgress(progress);
			if (num >= 100) return '#28a745'; // 绿色 - 已完成
			if (num >= 50) return '#007bff';  // 蓝色 - 进行中
			if (num > 0) return '#ffc107';    // 黄色 - 刚开始
			return '#6c757d';                 // 灰色 - 未开始
		},

		// 获取质检结果文本
		getInspectionResultText(result) {
			const options = this.dictOptions?.inspection_result;
			if (!options || !Array.isArray(options) || options.length === 0) {
				return result || '未知';
			}
			return getDictLabel(options, result) || '未知';
		},

		// 获取质检结果颜色
		getInspectionResultColor(result) {
			const resultNum = parseInt(result) || 0;
			if (resultNum === 1) return '#198754'; // 绿色 - 合格
			if (resultNum === 2) return '#dc3545'; // 红色 - 不合格
			return '#6c757d';                      // 灰色 - 未知
		},

		// 获取审批状态文本
		getApproveStatusText(status) {
			const options = this.dictOptions?.approve_status;
			if (!options || !Array.isArray(options) || options.length === 0) {
				return status || '未知';
			}
			return getDictLabel(options, status) || '未知';
		},

		// 获取审批状态颜色
		getApproveStatusColor(status) {
			const statusNum = parseInt(status);
			switch (statusNum) {
				case 0: return '#6c757d'; // 灰色 - 待提交 (info)
				case 1: return '#0d6efd'; // 蓝色 - 待审核 (primary)
				case 2: return '#0d6efd'; // 蓝色 - 审核中 (primary)
				case 3: return '#198754'; // 绿色 - 通过 (success)
				case 4: return '#dc3545'; // 红色 - 不通过 (danger)
				case 5: return '#6c757d'; // 灰色 - 已反审核 (info)
				default: return '#6c757d'; // 灰色 - 未知状态
			}
		},

		// 获取仓库名称
		getWarehouseName(warehouseId) {
			if (!warehouseId) return '';

			// 从仓库选项中查找对应的仓库名称
			if (this.warehouseOptions && Array.isArray(this.warehouseOptions)) {
				const warehouse = this.warehouseOptions.find(item => {
					// 支持字符串和数字类型的ID比较
					return String(item.id) === String(warehouseId) ||
						   Number(item.id) === Number(warehouseId);
				});

				if (warehouse) {
					// 优先返回name，如果没有name则尝试其他可能的名称字段
					return warehouse.name || warehouse.warehouseName || warehouse.title || warehouse.label;
				}
			}

			// 如果找不到对应的仓库名称，返回ID
			return String(warehouseId);
		},

		// 获取单位名称
		getUnitName(unitId) {
			if (!unitId) return '';
			return this.unitMap.get(parseInt(unitId)) || unitId.toString();
		},

		// 格式化数量
		formatQuantity(quantity) {
			if (quantity === null || quantity === undefined || quantity === '') {
				return '-';
			}
			const num = parseFloat(quantity);
			if (isNaN(num)) {
				return '-';
			}
			return num.toString();
		},

		// 格式化金额
		formatAmount(amount) {
			if (amount === null || amount === undefined || amount === '') {
				return '-';
			}
			const num = parseFloat(amount);
			if (isNaN(num)) {
				return '-';
			}
			return num.toFixed(2);
		},

		// 获取业务类型文本
		getBizTypeText(bizType) {
			if (!bizType) return '-';

			// 使用字典数据
			const options = this.dictOptions?.inventory_transaction_type;
			if (options && Array.isArray(options) && options.length > 0) {
				const dictLabel = getDictLabel(options, bizType);
				if (dictLabel) return dictLabel;
			}

			// 如果字典数据不可用，直接返回原值
			return bizType;
		},

		// 格式化日期
		formatDate(dateStr) {
			if (!dateStr) return '-';
			try {
				const date = new Date(dateStr);
				if (isNaN(date.getTime())) return '-';
				return date.toLocaleDateString('zh-CN', {
					year: 'numeric',
					month: '2-digit',
					day: '2-digit'
				});
			} catch (error) {
				return '-';
			}
		},

		// 获取任务类型文本
		getTaskTypeText(type) {
			const options = this.dictOptions?.mfg_work_type;
			if (!options || !Array.isArray(options) || options.length === 0) {
				return type || '未知';
			}
			return getDictLabel(options, type) || '未知';
		},

		// 获取质检来源类型文本
		getInspectionSourceTypeText(sourceType) {
			const options = this.dictOptions?.inspect_source_type;
			if (!options || !Array.isArray(options) || options.length === 0) {
				return sourceType || '未知';
			}
			return getDictLabel(options, sourceType) || '未知';
		},

		// 获取质检状态文本
		getInspectionStatusText(status) {
			const options = this.dictOptions?.inspect_status;
			if (!options || !Array.isArray(options) || options.length === 0) {
				return status || '未知';
			}
			return getDictLabel(options, status) || '未知';
		},

		// 获取质检结果样式类
		getInspectionResultClass(result) {
			switch (result) {
				case 1: return 'result-pass';      // 合格
				case 2: return 'result-fail';      // 不合格
				case 3: return 'result-pending';   // 待检
				default: return 'result-unknown';  // 未知
			}
		},

		// 获取产品来源类型文本
		getProductSourceTypeText(sourceType) {
			const options = this.dictOptions?.product_source_type;
			if (!options || !Array.isArray(options) || options.length === 0) {
				return sourceType || '未知';
			}
			return getDictLabel(options, sourceType) || '未知';
		},

		// 获取业务类型文本（产品入库）
		getProductBizTypeText(bizType) {
			const options = this.dictOptions?.scm_biz_type;
			if (!options || !Array.isArray(options) || options.length === 0) {
				return bizType || '未知';
			}
			return getDictLabel(options, bizType) || '未知';
		},

		// 格式化时长（分钟转换为时分格式）
		formatDuration(minutes) {
			if (!minutes || minutes === 0) return '-';
			const mins = parseInt(minutes);
			if (mins < 60) {
				return `${mins}分钟`;
			}
			const hours = Math.floor(mins / 60);
			const remainingMins = mins % 60;
			if (remainingMins === 0) {
				return `${hours}小时`;
			}
			return `${hours}小时${remainingMins}分钟`;
		}
	}
}
</script>

<style lang="scss" scoped>
.work-order-detail {
	min-height: 100vh;
	background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

/* 页面内容 */
.page-content {
	padding: 0;
	height: 100vh;
}


/* Tab导航 */
.tab-nav {
	display: flex;
	background: white;
	border-radius: 0;
	margin: 0;
	border-bottom: 1px solid #f0f0f0;
	overflow: hidden;
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	z-index: 999;
	padding: 0 24rpx;
}

.tab-item {
	flex: 1;
	padding: 32rpx 16rpx;
	text-align: center;
	font-size: 28rpx;
	color: #666;
	background: transparent;
	transition: all 0.3s ease;
	position: relative;
	font-weight: 400;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;

	&.active {
		color: #007AFF;
		font-weight: 500;

		&::after {
			content: '';
			position: absolute;
			bottom: 0;
			left: 50%;
			transform: translateX(-50%);
			width: 40rpx;
			height: 4rpx;
			background: #007AFF;
			border-radius: 2rpx;
		}
	}
}

/* Tab内容 */
.tab-content {
	background: white;
	border-radius: 0;
	height:100%;
	margin-top: 120rpx;
	box-shadow: none;
}

.tab-pane {
	padding: 24rpx;
	padding-bottom: 120rpx; /* 为底部操作栏留出空间 */
}

/* 信息卡片 */
.info-card, .action-card {
	margin: 0 0 24rpx;
	background-color: white;
	border-radius: 20rpx;
	overflow: hidden;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
	border: 1px solid rgba(0, 0, 0, 0.05);
}

.card-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 32rpx 32rpx 24rpx;
	background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
	border-bottom: 1px solid rgba(0, 0, 0, 0.06);
}

.card-title {
	font-size: 32rpx;
	font-weight: 700;
	color: #2c3e50;
	position: relative;

	&::before {
		content: '';
		position: absolute;
		left: -16rpx;
		top: 50%;
		transform: translateY(-50%);
		width: 6rpx;
		height: 24rpx;
		background: linear-gradient(135deg, #007bff, #0056b3);
		border-radius: 3rpx;
	}
}

.card-content {
	padding: 24rpx 32rpx 32rpx;
}

/* 信息行 */
.info-row {
	display: flex;
	align-items: flex-start;
	margin-bottom: 20rpx;
	padding: 16rpx 0;
	border-bottom: 1px solid rgba(0, 0, 0, 0.04);

	&:last-child {
		margin-bottom: 0;
		border-bottom: none;
	}

	.label {
		font-size: 28rpx;
		color: #6c757d;
		min-width: 160rpx;
		margin-right: 24rpx;
		font-weight: 500;
		line-height: 1.4;
	}

	.value {
		font-size: 28rpx;
		color: #2c3e50;
		flex: 1;
		word-break: break-all;
		font-weight: 400;
		line-height: 1.4;

		&.highlight {
			color: #007bff;
			font-weight: 700;
			font-size: 30rpx;
		}

		&.material-name-text {
			white-space: nowrap;
			overflow: hidden;
			text-overflow: ellipsis;
			word-break: normal;
		}
	}
}

/* 底部固定操作栏 */
.bottom-action-bar {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	background-color: white;
	border-top: 1px solid #e5e5e5;
	padding: 20rpx 0;
	display: flex;
	justify-content: center;
	align-items: center;
	box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.1);
	z-index: 1000;
}

.action-item {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 16rpx 32rpx;
	border-radius: 12rpx;
	transition: all 0.3s ease;
	cursor: pointer;

	&:active {
		background-color: #f8f9fa;
		transform: scale(0.95);
	}
}

.action-text {
	font-size: 24rpx;
	color: #007bff;
	margin-top: 8rpx;
	font-weight: 500;
}

/* 状态标签 */
.status-badge {
	padding: 12rpx 24rpx;
	border-radius: 16rpx;
	font-size: 26rpx;
	font-weight: 700;
	color: white;
	text-align: center;
	box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.2);
	border: none;
	text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.3);
	min-width: 80rpx;

	&.small {
		padding: 10rpx 20rpx;
		font-size: 24rpx;
		border-radius: 12rpx;
		font-weight: 700;
		min-width: 70rpx;
	}
}

/* 进度相关 */
.progress-text {
	font-size: 30rpx;
	color: #2c3e50;
	font-weight: 700;
	background: linear-gradient(135deg, #007bff, #0056b3);
	-webkit-background-clip: text;
	-webkit-text-fill-color: transparent;
	background-clip: text;
}

.progress-bar {
	height: 20rpx;
	background: linear-gradient(135deg, #e9ecef, #dee2e6);
	border-radius: 10rpx;
	overflow: hidden;
	margin-top: 20rpx;
	box-shadow: inset 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
	position: relative;
}

.progress-fill {
	height: 100%;
	border-radius: 10rpx;
	transition: width 0.6s cubic-bezier(0.4, 0, 0.2, 1);
	position: relative;

	&::after {
		content: '';
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
		animation: shimmer 2s infinite;
	}
}

@keyframes shimmer {
	0% { transform: translateX(-100%); }
	100% { transform: translateX(100%); }
}

/* 状态网格 */
.status-grid {
	display: grid;
	grid-template-columns: 1fr 1fr;
	gap: 24rpx;
}

.status-item {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 20rpx 24rpx;
	background: white;
	border-radius: 16rpx;
	border: 2px solid #f0f0f0;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);

	.status-label {
		font-size: 28rpx;
		color: #2c3e50;
		font-weight: 600;
	}
}

/* 列表项 */
.list-item {
	margin-bottom: 24rpx;
	padding: 24rpx;
	background: #f8f9fa;
	border-radius: 16rpx;
	border: 1px solid #e9ecef;

	&:last-child {
		margin-bottom: 0;
	}
}

/* 空状态 */
.empty-state {
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 80rpx 24rpx;
	text-align: center;
	height: 100%;
	.empty-text {
		font-size: 28rpx;
		color: #6c757d;
		font-weight: 500;
	}
}

/* 加载状态 */
.loading-container {
	display: flex;
	align-items: center;
	justify-content: center;
	height: 100vh;
	background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);

	&::before {
		content: '';
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: radial-gradient(circle at 50% 50%, rgba(0, 123, 255, 0.1) 0%, transparent 70%);
		animation: pulse 2s ease-in-out infinite;
	}
}

@keyframes pulse {
	0%, 100% { opacity: 0.5; }
	50% { opacity: 1; }
}

/* 响应式设计 */
@media (max-width: 750rpx) {
	.page-content {
		padding: 0;
	}

	.tab-nav {
		padding: 0 16rpx;
	}

	.tab-item {
		padding: 28rpx 12rpx;
		font-size: 26rpx;
	}

	.tab-content {
		border-radius: 0;
		margin-top: 100rpx;
		height:100%;
	}

	.tab-pane {
		padding: 20rpx;
	}

	.info-card {
		margin: 0 0 20rpx;
		border-radius: 16rpx;
	}

	.card-header {
		padding: 24rpx 24rpx 20rpx;
	}

	.card-content {
		padding: 20rpx 24rpx 24rpx;
	}

	.info-row {
		.label {
			min-width: 140rpx;
			font-size: 26rpx;
		}

		.value {
			font-size: 26rpx;
		}
	}

	.status-grid {
		grid-template-columns: 1fr;
		gap: 16rpx;
	}

	.list-item {
		margin-bottom: 20rpx;
		padding: 20rpx;
		border-radius: 12rpx;
	}

	/* 报工相关响应式样式 */
	.production-grid {
		grid-template-columns: 1fr;
		gap: 12rpx;
	}

	.time-info-grid {
		gap: 12rpx;
	}

	.report-header {
		padding: 20rpx 24rpx;
		flex-direction: column;
		align-items: flex-start;
		gap: 12rpx;
	}

	.report-title-section {
		width: 100%;
		justify-content: space-between;
	}

	.report-code {
		font-size: 28rpx;
	}

	.task-type-badge {
		font-size: 20rpx;
		padding: 6rpx 12rpx;
	}

	.time-section,
	.production-section,
	.quality-section,
	.remark-section {
		padding: 20rpx 24rpx;
	}

	.section-title {
		font-size: 24rpx;
	}

	.time-info-item,
	.quality-item {
		padding: 10rpx 12rpx;
	}

	.production-item {
		padding: 12rpx;
	}

	.production-label,
	.time-label,
	.quality-label {
		font-size: 22rpx;
	}

	.production-value,
	.time-value,
	.quality-value {
		font-size: 24rpx;
	}

	.production-value.highlight {
		font-size: 26rpx;
	}

	.remark-text {
		font-size: 24rpx;
		padding: 12rpx;
	}
}

/* 领料单分组样式 */
.receipt-group {
	margin-bottom: 32rpx;

	&:last-child {
		margin-bottom: 0;
	}
}

.receipt-header {
	margin-bottom: 16rpx;
	border-left: 4rpx solid #007AFF;

	.card-title {
		color: #007AFF;
		font-weight: 600;
	}
}

.detail-list {
	margin-left: 16rpx;

	.detail-item {
		margin-bottom: 16rpx;
		border-left: 2rpx solid #e9ecef;
		background: #f8f9fa;

		&:last-child {
			margin-bottom: 0;
		}

		.card-title {
			color: #6c757d;
			font-size: 26rpx;
		}
	}
}

/* 单位样式 */
.unit {
	margin-left: 8rpx;
	color: #6c757d;
	font-size: 24rpx;
	font-weight: normal;
}

/* 卡片副标题 */
.card-subtitle {
	color: #6c757d;
	font-size: 24rpx;
	font-weight: normal;
}

/* 报工单列表样式 */
.report-list {
	margin-top: 24rpx;
}

.report-item {
	background: white;
	border-radius: 16rpx;
	margin-bottom: 24rpx;
	box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
	overflow: hidden;
	border: 1px solid rgba(0, 0, 0, 0.05);
}

.report-header {
	background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
	padding: 24rpx 32rpx;
	border-bottom: 1px solid #e9ecef;
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.report-title-section {
	display: flex;
	align-items: center;
	gap: 16rpx;
}

.report-code {
	font-size: 30rpx;
	font-weight: 700;
	color: #2c3e50;
}

.task-type-badge {
	background: #007bff;
	color: white;
	padding: 8rpx 16rpx;
	border-radius: 12rpx;
	font-size: 22rpx;
	font-weight: 600;
}

.report-time {
	font-size: 24rpx;
	color: #6c757d;
}

/* 报工信息区块样式 */
.section-title {
	font-size: 26rpx;
	font-weight: 600;
	color: #495057;
	margin-bottom: 16rpx;
	padding-bottom: 8rpx;
	border-bottom: 2rpx solid #e9ecef;
}

.time-section,
.production-section,
.quality-section,
.remark-section {
	padding: 24rpx 32rpx;
	border-bottom: 1px solid rgba(0, 0, 0, 0.04);
}

.time-section:last-child,
.production-section:last-child,
.quality-section:last-child,
.remark-section:last-child {
	border-bottom: none;
}

/* 时间信息网格 */
.time-info-grid {
	display: grid;
	grid-template-columns: 1fr;
	gap: 16rpx;
}

.time-info-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 12rpx 16rpx;
	background: #f8f9fa;
	border-radius: 8rpx;
}

.time-label {
	font-size: 26rpx;
	color: #6c757d;
	font-weight: 500;
}

.time-value {
	font-size: 26rpx;
	color: #2c3e50;
	font-weight: 600;
}

.time-value.duration {
	color: #007bff;
	font-weight: 700;
}

/* 生产数据网格 */
.production-grid {
	display: grid;
	grid-template-columns: 1fr 1fr;
	gap: 16rpx;
}

.production-item {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 16rpx;
	background: #f8f9fa;
	border-radius: 12rpx;
	border: 2px solid transparent;
	transition: all 0.3s ease;
}

.production-item:hover {
	border-color: #007bff;
	background: #e3f2fd;
}

.production-label {
	font-size: 24rpx;
	color: #6c757d;
	margin-bottom: 8rpx;
}

.production-value {
	font-size: 28rpx;
	color: #2c3e50;
	font-weight: 600;
}

.production-value.highlight {
	color: #007bff;
	font-weight: 700;
	font-size: 30rpx;
}

/* 质量环境网格 */
.quality-grid {
	display: grid;
	grid-template-columns: 1fr;
	gap: 12rpx;
}

.quality-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 12rpx 16rpx;
	background: #f8f9fa;
	border-radius: 8rpx;
}

.quality-label {
	font-size: 26rpx;
	color: #6c757d;
}

.quality-value {
	font-size: 26rpx;
	color: #2c3e50;
	font-weight: 600;
}

/* 备注样式 */
.remark-text {
	font-size: 26rpx;
	color: #495057;
	line-height: 1.5;
	background: #f8f9fa;
	padding: 16rpx;
	border-radius: 8rpx;
	border-left: 4rpx solid #007bff;
}

/* 领料单主卡片样式 */
.receipt-main-card {
	background: #ffffff;
	border-radius: 16rpx;
	margin: 0 0 32rpx 0;
	box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
	overflow: hidden;
}

.receipt-main-header {
	background: #f8f9fa;
	border-bottom: 1rpx solid #e9ecef;
	padding: 20rpx 32rpx;
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.main-header-content {
	flex: 1;
}

.main-card-title {
	color: #2c3e50;
	font-size: 32rpx;
	font-weight: 600;
	line-height: 1.2;
}

.main-card-subtitle {
	color: #6c757d;
	font-size: 24rpx;
	margin-top: 8rpx;
	display: block;
}

.receipt-main-info {
	padding: 24rpx 32rpx;
	background: #f8f9fa;
	border-bottom: 1rpx solid #e9ecef;
}

.main-info-row {
	display: flex;
	align-items: center;
	margin-bottom: 12rpx;
}

.main-info-row:last-child {
	margin-bottom: 0;
}

.main-info-label {
	color: #6c757d;
	font-size: 24rpx;
	margin-right: 16rpx;
	min-width: 120rpx;
}

.main-info-value {
	color: #333333;
	font-size: 24rpx;
	flex: 1;
}

/* 物料网格样式 */
.material-grid {
	padding: 16rpx 16rpx 24rpx;
	display: flex;
	flex-wrap: wrap;
	justify-content: space-between;
}

.material-item {
	width: calc(50% - 8rpx);
	background: #ffffff;
	border: 1rpx solid #e9ecef;
	border-radius: 12rpx;
	margin-bottom: 16rpx;
	overflow: hidden;
	box-shadow: 0 1rpx 6rpx rgba(0, 0, 0, 0.05);
}

.material-header {
	background: #f8f9fa;
	padding: 16rpx 20rpx;
	border-bottom: 1rpx solid #e9ecef;
}

.material-name {
	color: #333333;
	font-size: 26rpx;
	font-weight: 500;
	line-height: 1.3;
	display: block;
	margin-bottom: 4rpx;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}

.material-code {
	color: #6c757d;
	font-size: 22rpx;
	line-height: 1.2;
}

.material-content {
	padding: 16rpx 20rpx;
}

.material-spec {
	margin-bottom: 16rpx;
}

.spec-text {
	color: #495057;
	font-size: 24rpx;
	background: #e9ecef;
	padding: 8rpx 12rpx;
	border-radius: 6rpx;
	display: inline-block;
}

.quantity-section {
	display: flex;
	flex-direction: column;
	gap: 12rpx;
}

.quantity-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.quantity-label {
	color: #6c757d;
	font-size: 22rpx;
	min-width: 60rpx;
}

.quantity-value {
	font-size: 24rpx;
	font-weight: 500;
	text-align: right;
	flex: 1;
}

.quantity-value.planned {
	color: #007AFF;
}

.quantity-value.fulfilled {
	color: #28a745;
}

.quantity-value.standard {
	color: #6c757d;
}

/* 价格信息样式 */
.price-section {
	margin-top: 16rpx;
	padding-top: 16rpx;
	border-top: 1rpx solid #e9ecef;
	display: flex;
	flex-direction: column;
	gap: 8rpx;
}

.price-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.price-label {
	color: #6c757d;
	font-size: 22rpx;
	min-width: 60rpx;
}

.price-value {
	color: #dc3545;
	font-size: 24rpx;
	font-weight: 500;
	text-align: right;
	flex: 1;
}

.material-extra {
	margin-top: 16rpx;
	padding-top: 16rpx;
	border-top: 1rpx solid #e9ecef;
}

.extra-item {
	display: flex;
	align-items: center;
	margin-bottom: 8rpx;
}

.extra-item:last-child {
	margin-bottom: 0;
}

.extra-label {
	color: #6c757d;
	font-size: 22rpx;
	margin-right: 8rpx;
}

.extra-value {
	color: #495057;
	font-size: 22rpx;
	flex: 1;
}

/* 质检单样式 */
.inspection-list {
	padding: 0 20rpx;
}

.inspection-item {
	background: #ffffff;
	border-radius: 16rpx;
	margin: 0 0 32rpx 0;
	padding: 32rpx;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
}

.inspection-header {
	display: flex;
	justify-content: space-between;
	align-items: flex-start;
	margin-bottom: 24rpx;
	padding-bottom: 16rpx;
	border-bottom: 1rpx solid #e9ecef;
}

.inspection-title-section {
	display: flex;
	align-items: center;
	gap: 12rpx;
}

.inspection-code {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
}

.inspection-result-badge {
	padding: 6rpx 12rpx;
	border-radius: 8rpx;
	font-size: 24rpx;
	font-weight: 500;
}

.result-pass {
	background: #e8f5e8;
	color: #52c41a;
}

.result-fail {
	background: #fff2f0;
	color: #ff4d4f;
}

.result-pending {
	background: #fff7e6;
	color: #fa8c16;
}

.result-unknown {
	background: #f5f5f5;
	color: #999;
}

.inspection-time {
	font-size: 24rpx;
	color: #999;
}

.inspection-basic-section,
.inspection-info-section,
.inspection-remark-section {
	margin-bottom: 20rpx;
}

.section-title {
	font-size: 28rpx;
	font-weight: 600;
	color: #333;
	margin-bottom: 16rpx;
}

.inspection-basic-grid,
.inspection-info-grid {
	display: grid;
	grid-template-columns: 1fr 1fr;
	gap: 16rpx;
}

.inspection-basic-item,
.inspection-info-item {
	display: flex;
	flex-direction: column;
	gap: 4rpx;
}

.basic-label,
.info-label {
	font-size: 24rpx;
	color: #666;
}

.basic-value,
.info-value {
	font-size: 28rpx;
	color: #333;
	font-weight: 500;
}

.inspection-remark-text {
	font-size: 28rpx;
	color: #333;
	line-height: 1.5;
	background: #f8f9fa;
	padding: 16rpx;
	border-radius: 8rpx;
}

/* 产品入库单样式 */
.product-receipt-list {
	padding: 0 20rpx;
}

.product-receipt-item {
	background: #ffffff;
	border-radius: 16rpx;
	margin: 0 0 32rpx 0;
	padding: 32rpx;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
}

.product-receipt-header {
	display: flex;
	justify-content: flex-start;
	align-items: center;
	margin-bottom: 32rpx;
	padding: 24rpx 0;
	border-bottom: 1rpx solid #e9ecef;
	min-height: 60rpx;
}

.product-receipt-title-section {
	display: flex;
	align-items: center;
	gap: 16rpx;
}

.product-receipt-code {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
	flex-shrink: 0;
}

.biz-type-badge {
	padding: 8rpx 16rpx;
	border-radius: 12rpx;
	font-size: 24rpx;
	font-weight: 500;
	background: #e6f7ff;
	color: #1890ff;
	white-space: nowrap;
	flex-shrink: 0;
}

.product-receipt-basic-section,
.product-receipt-details-section,
.product-receipt-remark-section {
	margin-bottom: 20rpx;
}

.product-receipt-basic-grid {
	display: grid;
	grid-template-columns: 1fr 1fr;
	gap: 16rpx;
}

.product-receipt-basic-item {
	display: flex;
	flex-direction: column;
	gap: 4rpx;
}

.product-receipt-details {
	background: #f8f9fa;
	border-radius: 12rpx;
	padding: 16rpx;
}

.detail-item {
	background: #fff;
	border-radius: 8rpx;
	padding: 16rpx;
	margin-bottom: 12rpx;
}

.detail-item:last-child {
	margin-bottom: 0;
}

.detail-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 12rpx;
	padding-bottom: 8rpx;
	border-bottom: 1rpx solid #f0f0f0;
}

.detail-material-name {
	font-size: 28rpx;
	font-weight: 600;
	color: #333;
}

.detail-material-code {
	font-size: 24rpx;
	color: #666;
}

.detail-info-grid {
	display: grid;
	grid-template-columns: 1fr 1fr;
	gap: 12rpx;
}

.detail-info-item {
	display: flex;
	flex-direction: column;
	gap: 4rpx;
}

.detail-label {
	font-size: 24rpx;
	color: #666;
}

.detail-value {
	font-size: 26rpx;
	color: #333;
	font-weight: 500;
}

.detail-value.highlight {
	color: #1890ff;
	font-weight: 600;
}

.product-receipt-remark-text {
	font-size: 28rpx;
	color: #333;
	line-height: 1.5;
	background: #f8f9fa;
	padding: 16rpx;
	border-radius: 8rpx;
	margin-bottom: 8rpx;
}


</style>
