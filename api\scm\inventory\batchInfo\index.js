import request from "../../../../utils/request";

/**
 * 批次信息相关API
 */

// 获取批次信息分页列表
export function getBatchInfoPageApi(params) {
  return request({
    url: '/scm/inventory/batch-info/page',
    method: 'GET',
    params,
  })
}

// 获取批次信息详情
export function getBatchInfoApi(id) {
  return request({
    url: `/scm/inventory/batch-info/get?id=${id}`,
    method: 'GET',
  })
}

// 新增批次信息
export function createBatchInfoApi(data) {
  return request({
    url: '/scm/inventory/batch-info/create',
    method: 'POST',
    data,
  })
}

// 修改批次信息
export function updateBatchInfoApi(data) {
  return request({
    url: '/scm/inventory/batch-info/update',
    method: 'PUT',
    data,
  })
}

// 删除批次信息
export function deleteBatchInfoApi(id) {
  return request({
    url: `/scm/inventory/batch-info/delete?id=${id}`,
    method: 'DELETE',
  })
}

// 导出批次信息Excel
export function exportBatchInfoApi(params) {
  return request({
    url: '/scm/inventory/batch-info/export-excel',
    method: 'GET',
    params,
  })
}

// 通过物料ID获取简单信息分页
export function getSimpleBatchInfoListByMaterialIdApi(params) {
  return request({
    url: '/scm/inventory/batch-info/get-by-material-id',
    method: 'GET',
    params,
  })
}

// 通过物料ID数组获取简单信息分页
export function getSimpleBatchInfoListByMaterialIdsApi(params) {
  return request({
    url: '/scm/inventory/batch-info/get-by-material-ids',
    method: 'GET',
    params,
  })
}
