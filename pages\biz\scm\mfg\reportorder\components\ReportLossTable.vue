<template>
	<view class="report-loss-table">
		<!-- 表格标题和操作按钮 -->
		<view class="table-header">
			<text class="table-title">报工损耗</text>
			<button class="btn-add" @click="handleAdd" :disabled="disabled">
				<uni-icons type="plus" size="16" color="#fff"></uni-icons>
				<text class="btn-text">新增</text>
			</button>
		</view>

		<!-- 损耗列表 -->
		<view class="loss-list" v-if="lossData.length > 0">
			<view
				class="loss-item"
				v-for="(item, index) in lossData"
				:key="item.id || index"
			>
				<!-- 物料信息 -->
				<view class="material-info">
					<view class="material-header">
						<view class="material-basic">
							<view class="material-name">
								<text class="label">物料名称：</text>
								<text class="value">{{ item.materialName || '-' }}</text>
							</view>
							<view class="material-code">
								<text class="label">物料编码：</text>
								<text class="value">{{ item.materialCode || '-' }}</text>
							</view>
						</view>
						<!-- 操作按钮 - 放在物料信息右侧 -->
						<view class="item-actions" v-if="!disabled">
							<button class="btn-edit" @click="handleEdit(item, index)">
								<uni-icons type="compose" size="14" color="#007AFF"></uni-icons>
							</button>
							<button class="btn-delete" @click="handleDelete(item, index)">
								<uni-icons type="trash" size="14" color="#ff4757"></uni-icons>
							</button>
						</view>
					</view>
					<view class="material-spec">
						<text class="label">规格：</text>
						<text class="value">{{ item.spec || '-' }}</text>
					</view>
				</view>

				<!-- 损耗信息 -->
				<view class="loss-info">
					<view class="loss-quantity">
						<text class="label">损耗数量：</text>
						<text class="value">{{ item.lossQuantity || 0 }}</text>
						<text class="unit">{{ item.lossUnitName || '' }}</text>
					</view>
					<view class="loss-remark" v-if="item.remark">
						<text class="label">备注：</text>
						<text class="value">{{ item.remark }}</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 空状态 -->
		<view class="empty-state" v-else>
			<uni-icons type="info" size="48" color="#c0c4cc"></uni-icons>
			<text class="empty-text">暂无损耗记录</text>
			<text class="empty-tip" v-if="!disabled">点击上方"新增"按钮添加损耗信息</text>
		</view>

		<!-- 损耗弹窗 -->
		<ReportLossModal
			ref="lossModalRef"
			:visible="modalVisible"
			:form-data="currentLossItem"
			:work-id="workId"
			@confirm="handleModalConfirm"
			@cancel="handleModalCancel"
		/>
	</view>
</template>

<script>
import { getReportLossByReportIdApi } from '../../../../../../api/scm/mfg/reportloss'
import ReportLossModal from './ReportLossModal.vue'

export default {
	name: 'ReportLossTable',
	components: {
		ReportLossModal
	},
	props: {
		// 报工单ID
		reportId: {
			type: [Number, String],
			default: null
		},
		// 工单ID
		workId: {
			type: [Number, String],
			default: null
		},
		// 是否禁用编辑
		disabled: {
			type: Boolean,
			default: false
		}
	},
	data() {
		return {
			// 损耗数据列表
			lossData: [],
			// 弹窗显示状态
			modalVisible: false,
			// 当前编辑的损耗项
			currentLossItem: null,
			// 当前编辑的索引
			currentIndex: -1,
			// 加载状态
			loading: false
		}
	},
	watch: {
		// 监听报工单ID变化，重新加载数据
		reportId: {
			handler(newVal) {
				if (newVal) {
					this.loadLossData()
				} else {
					this.lossData = []
				}
			},
			immediate: true
		}
	},
	methods: {
		// 加载损耗数据
		async loadLossData() {
			if (!this.reportId) return

			try {
				this.loading = true
				const response = await getReportLossByReportIdApi(this.reportId)
				if (response && response.data) {
					this.lossData = Array.isArray(response.data) ? response.data : []
				} else {
					this.lossData = []
				}
			} catch (error) {
				console.error('加载损耗数据失败:', error)
				this.lossData = []
				uni.showToast({
					title: '加载损耗数据失败',
					icon: 'none'
				})
			} finally {
				this.loading = false
			}
		},

		// 新增损耗
		handleAdd() {
			if (this.disabled) return

			this.currentLossItem = {
				id: null,
				reportId: this.reportId,
				workId: this.workId,
				materialId: null,
				materialName: '',
				materialCode: '',
				spec: '',
				lossQuantity: 0,
				lossUnit: null,
				lossUnitName: '',
				remark: ''
			}
			this.currentIndex = -1
			this.modalVisible = true
		},

		// 编辑损耗
		handleEdit(item, index) {
			if (this.disabled) return

			// 使用索引从数组中获取数据，确保数据正确
			const targetItem = this.lossData[index]
			if (targetItem) {
				this.currentLossItem = { ...targetItem }
				this.currentIndex = index
				this.modalVisible = true
			}
		},

		// 删除损耗
		async handleDelete(item, index) {
			if (this.disabled) return

			try {
				await uni.showModal({
					title: '确认删除',
					content: '确定要删除这条损耗记录吗？',
					confirmText: '删除',
					cancelText:'取消'
				})

				// 从本地数据中移除（不调用API）
				this.lossData.splice(index, 1)

				uni.showToast({
					title: '删除成功',
					icon: 'success'
				})

				// 通知父组件数据变化
				this.$emit('change', this.lossData)

			} catch (error) {
				if (error.errMsg && error.errMsg.includes('cancel')) {
					// 用户取消删除
					return
				}
				console.error('删除损耗失败:', error)
				uni.showToast({
					title: '删除失败',
					icon: 'error'
				})
			}
		},

		// 弹窗确认
		handleModalConfirm(formData) {
			// 本地数据管理，不调用API
			const updatedData = {
				...formData,
				// 为新增的记录生成临时ID
				id: formData.id || Date.now()
			}

			if (this.currentIndex >= 0) {
				// 编辑模式：更新现有记录
				this.lossData.splice(this.currentIndex, 1, updatedData)
			} else {
				// 新增模式：添加新记录
				this.lossData.push(updatedData)
			}

			this.modalVisible = false
			uni.showToast({
				title: this.currentIndex >= 0 ? '修改成功' : '新增成功',
				icon: 'success'
			})

			// 通知父组件数据变化
			this.$emit('change', this.lossData)
		},

		// 弹窗取消
		handleModalCancel() {
			this.modalVisible = false
		},

		// 获取损耗数据（供父组件调用）
		getLossData() {
			return this.lossData
		},

		// 设置损耗数据（供父组件调用）
		setLossData(data) {
			this.lossData = Array.isArray(data) ? data : []
		}
	}
}
</script>

<style scoped>
.report-loss-table {
	background-color: #fff;
	border-radius: 12px;
	padding: 16px;
	margin-top: 16px;
	box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
	border: 1px solid #f0f0f0;
}

/* 表格标题 */
.table-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 16px;
	padding-bottom: 12px;
	border-bottom: 1px solid #f0f0f0;
}

.table-title {
	font-size: 16px;
	font-weight: 600;
	color: #333;
}

.btn-add {
	display: flex;
	align-items: center;
	gap: 6px;
	padding: 8px 16px;
	background: linear-gradient(135deg, #007AFF 0%, #0056CC 100%);
	color: #fff;
	border: none;
	border-radius: 8px;
	font-size: 14px;
	font-weight: 500;
	box-shadow: 0 2px 8px rgba(0, 122, 255, 0.3);
	transition: all 0.2s ease;
}

.btn-add:active {
	transform: scale(0.98);
}

.btn-add:disabled {
	background: #e9ecef;
	color: #6c757d;
	box-shadow: none;
}

.btn-text {
	color: inherit;
}

/* 损耗列表 */
.loss-list {
	display: flex;
	flex-direction: column;
	gap: 12px;
}

.loss-item {
	display: flex;
	flex-direction: column;
	gap: 12px;
	padding: 16px;
	background-color: #f8f9fa;
	border-radius: 8px;
	border: 1px solid #e9ecef;
	position: relative;
}

/* 物料信息 */
.material-info {
	display: flex;
	flex-direction: column;
	gap: 8px;
}

.material-header {
	display: flex !important;
	flex-direction: row !important;
	justify-content: space-between;
	align-items: center;
	gap: 12px;
	width: 100%;
}

.material-basic {
	flex: 1;
	display: flex;
	flex-direction: column;
	gap: 8px;
	min-width: 0; /* 防止flex子项溢出 */
}

.material-name,
.material-code,
.material-spec {
	display: flex;
	align-items: center;
	font-size: 14px;
	line-height: 1.4;
}

.material-name .label {
	color: #333;
	font-weight: 500;
	min-width: 80px;
}

.material-name .value {
	color: #007AFF;
	font-weight: 500;
}

.material-code .label,
.material-spec .label {
	color: #666;
	min-width: 80px;
}

.material-code .value,
.material-spec .value {
	color: #333;
}

/* 损耗信息 */
.loss-info {
	display: flex;
	flex-direction: column;
	gap: 8px;
}

.loss-quantity,
.loss-remark {
	display: flex;
	align-items: center;
	font-size: 14px;
	line-height: 1.4;
}

.loss-quantity .label,
.loss-remark .label {
	color: #666;
	min-width: 80px;
}

.loss-quantity .value {
	color: #ff6b35;
	font-weight: 600;
	margin-right: 4px;
}

.loss-quantity .unit {
	color: #666;
	font-size: 12px;
}

.loss-remark .value {
	color: #333;
}

/* 操作按钮 */
.item-actions {
	display: flex;
	gap: 8px;
	flex-shrink: 0;
	align-items: center;
	white-space: nowrap;
}

.btn-edit,
.btn-delete {
	display: flex;
	align-items: center;
	justify-content: center;
	width: 32px;
	height: 32px;
	border: none;
	border-radius: 6px;
	background-color: #fff;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
	transition: all 0.2s ease;
}

.btn-edit:active,
.btn-delete:active {
	transform: scale(0.95);
}

/* 空状态 */
.empty-state {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 40px 20px;
	text-align: center;
}

.empty-text {
	font-size: 16px;
	color: #666;
	margin-top: 12px;
	font-weight: 500;
}

.empty-tip {
	font-size: 14px;
	color: #999;
	margin-top: 8px;
}

/* 响应式适配 */
@media screen and (max-width: 768px) {
	.loss-item {
		padding: 12px;
	}

	.item-actions {
		justify-content: flex-end;
	}

	.material-info,
	.loss-info {
		gap: 6px;
	}

	.material-name .label,
	.material-code .label,
	.material-spec .label,
	.loss-quantity .label,
	.loss-remark .label {
		min-width: 70px;
		font-size: 13px;
	}

	.material-name .value,
	.material-code .value,
	.material-spec .value,
	.loss-quantity .value,
	.loss-remark .value {
		font-size: 13px;
	}
}
</style>
